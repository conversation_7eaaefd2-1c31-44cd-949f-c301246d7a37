{"name": "hikacord-bot", "version": "1.0.0", "description": "A professional Discord bot built with discord.js and TypeScript", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["discord", "bot", "typescript", "discord.js"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"discord.js": "^14.14.1", "node-cron": "^3.0.3", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "typescript": "^5.3.3", "ts-node": "^10.9.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}